import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/buttons/common_button.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:kitemite_app/features/product_detail/ui/product_detail_screen.dart';
import 'package:kitemite_app/features/shopping_cart/provider/cart_provider.dart';
import 'package:kitemite_app/features/shopping_cart/provider/check_cart/check_cart_provider.dart';
import 'package:kitemite_app/features/shopping_cart/ui/widget/dialog_help_open_lock.dart';
import 'package:kitemite_app/features/shopping_cart/ui/widget/shopping_cart_item.dart';
import 'package:kitemite_app/features/shopping_cart/widget/dialog_open_lock_widget.dart';
import 'package:kitemite_app/gen/assets.gen.dart';
import 'package:kitemite_app/routing/router_paths.dart';
import 'package:open_settings_plus/core/open_settings_plus.dart';
import 'package:permission_handler/permission_handler.dart';

class ShoppingCartScreen extends ConsumerStatefulWidget {
  const ShoppingCartScreen({super.key});

  @override
  ConsumerState<ShoppingCartScreen> createState() => _ShoppingCartScreenState();
}

final statusLockProvider = StateProvider((_) => false);

class _ShoppingCartScreenState extends ConsumerState<ShoppingCartScreen>
    with WidgetsBindingObserver {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadCartItems();
    });
  }

  bool _isAtLeastOneCabinetComplete() {
    final cartItems = ref.read(cartNotifierProvider);
    final productImages = cartItems.productImages;

    for (var cabinet in cartItems.cabinets) {
      bool allItemsPhotographed = true;
      for (var item in cabinet['items'] as List) {
        final itemId = item['id'].toString();
        final quantity = item['quantity'] as int;

        for (int i = 0; i < quantity; i++) {
          final fullItemId = '${itemId}_$i';
          final hasImage = productImages.any((img) =>
              img.id.toString() == itemId &&
              img.itemId == fullItemId &&
              img.img.isNotEmpty);

          if (!hasImage) {
            allItemsPhotographed = false;
            break;
          }
        }

        if (!allItemsPhotographed) break;
      }

      if (allItemsPhotographed) return true;
    }

    return false;
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _loadCartItems() async {
    await ref.read(cartNotifierProvider.notifier).loadCartItems();
  }

  /// Kiểm tra quyền Bluetooth và Location
  Future<bool> _checkPermissions() async {
    var isDenied = false;
    Map<Permission, PermissionStatus> statuses = await [
      Permission.location,
      Permission.bluetooth,
      Permission.bluetoothScan,
      Permission.bluetoothConnect,
    ].request();

    for (var status in statuses.entries) {
      if (status.key == Permission.location) {
        if (status.value.isGranted) {
          //debugPrint('Location permission granted');
        } else {
          isDenied = true;
          // debugPrint("Location permission not granted");
        }
      } else if (status.key == Permission.bluetooth) {
        if (status.value.isGranted) {
        } else {
          isDenied = true;
          // debugPrint('Bluetooth scan permission not granted');
        }
      }
    }

    if (isDenied) {
      showAlertDialog(context);
      return false;
    }

    return true;
  }

  /// Cập nhật trạng thái khóa sau khi mở thành công
  void _updateLockStatus(String cabinetCode, bool isUnlocked) {
    ref.read(statusLockProvider.notifier).state = isUnlocked;
    ref
        .read(cartNotifierProvider.notifier)
        .updateCabinetLockStatus(cabinetCode, isUnlocked);
  }

  /// Hiển thị dialog mở khóa
  Future<bool?> _showUnlockDialog(String lockId, String cabinetCode) async {
    bool ispush = false;
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: DialogOpenLockWidget(
          lockId: lockId,
          openSuccess: () {
            if (ispush) return;
            ispush = true;
            _updateLockStatus(cabinetCode, true);
          },
          onTapByPass: () async {
            if (ispush) return;

            // final confirmed = await showBypassConfirmDialog(context, false);
            final confirmed = await showBypassConfirmDialogNew(context, false);
            if (confirmed == true) {
              ispush = true;
              _updateLockStatus(cabinetCode, true);
            }
          },
        ),
      ),
    );

    if (result == true) {
      _updateLockStatus(cabinetCode, true);
    }

    return result;
  }

  /// Kiểm tra cart và lấy thông tin lock
  Future<String?> _checkCartAndGetLockId(Map<String, dynamic> cabinet) async {
    final firstItem = (cabinet['items'] as List).first;
    await ref
        .read(checkCartNotifierProvider.notifier)
        .checkCart(int.parse(firstItem['id'].toString()));

    final checkCartState = ref.read(checkCartNotifierProvider);

    if (checkCartState.error != null) {
      context.showErrorSnackBar(checkCartState.error!);
      return null;
    }

    if (checkCartState.cartItems.first.lock?.id == null) {
      context.showErrorSnackBar('情報が空のロック');
      return null;
    }

    return checkCartState.cartItems.first.lock?.name ?? "";
  }

  /// Xử lý logic kết nối khóa chính
  Future<void> _handleConnectLock(Map<String, dynamic> cabinet) async {
    final cabinetCode = cabinet['cabinet_code'] as String;

    final hasPermission = await _checkPermissions();
    if (!hasPermission) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await _showUnlockDialog("lockId", cabinetCode);
      // final lockId = await _checkCartAndGetLockId(cabinet);
      // if (lockId != null && lockId.isNotEmpty) {
      //   await _showUnlockDialog(lockId, cabinetCode);
      // }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  showAlertDialog(BuildContext context) {
    // set up the button
    Widget okButton = TextButton(
      child: const Text("設定へ"),
      onPressed: () {
        Navigator.of(context).pop();
        if (Platform.isIOS) {
          const OpenSettingsPlusIOS().bluetooth();
        } else {
          const OpenSettingsPlusAndroid().bluetooth();
        }
      },
    );

    Widget cancelButton = TextButton(
      child: const Text("閉じる"),
      onPressed: () {
        Navigator.of(context).pop();
        Navigator.of(context).pop();
      },
    );

    // set up the AlertDialog
    AlertDialog alert = AlertDialog(
      content: const Text("Bluetoothの使用権限がありません。権限を許可してください。"),
      actions: [
        cancelButton,
        okButton,
      ],
    );

    // show the dialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final cartItems = ref.watch(cartNotifierProvider);
    final openLock = ref.watch(statusLockProvider);
    final storeName = cartItems.cabinets.isNotEmpty
        ? cartItems.cabinets.first['store_name'] as String?
        : null;

    return SafeArea(
      top: false,
      maintainBottomViewPadding: false,
      child: Column(
        children: [
          SizedBox(height: 8.h),
          Text(storeName ?? "ショッピングカート",
              style: AppTextStyles.bold(20.sp, color: AppColors.textPrimary)),
          Expanded(
            child: cartItems.isLoading
                ? const Center(child: CircularProgressIndicator())
                : cartItems.cabinets.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.shopping_cart_outlined,
                              size: 64.sp,
                              color: AppColors.textLightSecondary,
                            ),
                            SizedBox(height: 16.h),
                            Text(
                              "カートは空です",
                              style: AppTextStyles.regular(
                                16.sp,
                                color: AppColors.textLightSecondary,
                              ),
                            ),
                          ],
                        ),
                      )
                    : Column(
                        children: [
                          Expanded(
                              child: RefreshIndicator(
                            onRefresh: () async {
                              // await ref
                              //     .read(cartNotifierProvider.notifier)
                              //     .loadCartItems();
                            },
                            child: ListView(
                              padding: const EdgeInsets.all(16),
                              children: cartItems.cabinets.map((cabinet) {
                                final isCabinetUnlocked =
                                    !(cabinet['is_locked'] as bool);
                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              '冷凍庫-${cabinet['cabinet_code']}',
                                              style: AppTextStyles.bold(14.sp,
                                                  color: AppColors.textPrimary),
                                            ),
                                          ],
                                        ),
                                        const Spacer(),
                                        ElevatedButton(
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: isCabinetUnlocked
                                                ? AppColors.success
                                                : AppColors.primary,
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 8, vertical: 4),
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                          ),
                                          onPressed: () =>
                                              _handleConnectLock(cabinet),
                                          child: Row(
                                            children: [
                                              if (_isLoading)
                                                SizedBox(
                                                  width: 12.w,
                                                  height: 12.w,
                                                  child:
                                                      const CircularProgressIndicator(
                                                    strokeWidth: 2,
                                                    valueColor:
                                                        AlwaysStoppedAnimation<
                                                                Color>(
                                                            Colors.white),
                                                  ),
                                                )
                                              else
                                                Text(
                                                  isCabinetUnlocked
                                                      ? "解除済み"
                                                      : "解除",
                                                  style: AppTextStyles.regular(
                                                      12.sp,
                                                      color: isCabinetUnlocked
                                                          ? Colors.white
                                                          : AppColors
                                                              .textPrimary),
                                                ),
                                              SizedBox(width: 8.w),
                                              const Icon(
                                                Icons.lock,
                                                color: AppColors.textPrimary,
                                              )
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 8),
                                    Column(
                                      children: (cabinet['items'] as List)
                                          .map<Widget>((item) {
                                        // Create multiple items based on quantity
                                        final List<Widget> items = [];
                                        for (int i = 0;
                                            i < (item['quantity'] as int);
                                            i++) {
                                          items.add(
                                            Slidable(
                                              enabled: !isCabinetUnlocked,
                                              key: ValueKey('${item['id']}_$i'),
                                              endActionPane: ActionPane(
                                                extentRatio: 0.15,
                                                motion: const ScrollMotion(),
                                                children: [
                                                  CustomSlidableAction(
                                                    onPressed: (context) {
                                                      if (isCabinetUnlocked) {
                                                        return;
                                                      }
                                                      ref
                                                          .read(
                                                              cartNotifierProvider
                                                                  .notifier)
                                                          .removeItem(
                                                              '${item['id']}_$i');
                                                    },
                                                    backgroundColor: Colors.red,
                                                    foregroundColor:
                                                        Colors.white,
                                                    padding:
                                                        const EdgeInsets.all(4),
                                                    child: Column(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      children: [
                                                        const Icon(
                                                          Icons.delete,
                                                          color: Colors.white,
                                                        ),
                                                        const SizedBox(
                                                            height: 2),
                                                        Text(
                                                          "消去",
                                                          style: AppTextStyles
                                                              .regular(12.sp,
                                                                  color: Colors
                                                                      .white),
                                                        ),
                                                      ],
                                                    ),
                                                  )
                                                ],
                                              ),
                                              child: InkWell(
                                                onTap: () {
                                                  context.push(
                                                      RouterPaths.productDetail,
                                                      extra:
                                                          ProductDetailScreenArg(
                                                              productId:
                                                                  item['id'],
                                                              isLoginWithAccount:
                                                                  true,
                                                              isUpdateProduct:
                                                                  false));
                                                },
                                                child: ShoppingCartItem(
                                                  image: item["image"],
                                                  title: item["title"],
                                                  price: item["price"],
                                                  quantity: 1,
                                                  productId:
                                                      item['id'].toString(),
                                                  shelfCode: item['shelf_code']
                                                      .toString(),
                                                  itemId: '${item['id']}_$i',
                                                  isCabinetUnlocked:
                                                      isCabinetUnlocked,
                                                ),
                                              ),
                                            ),
                                          );
                                        }
                                        return Column(children: items);
                                      }).toList(),
                                    ),
                                    const SizedBox(height: 16),
                                  ],
                                );
                              }).toList(),
                            ),
                          )),
                          Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            child: CommonButton(
                              text: "セルフレジに進む",
                              onPressed: () {
                                // Check if any cabinet is unlocked
                                bool hasUnlockedCabinet = cartItems.cabinets
                                    .any((cabinet) =>
                                        !(cabinet['is_locked'] as bool));

                                if (!hasUnlockedCabinet) {
                                  context
                                      .showErrorSnackBar("鍵を開けてから購入を進めてください");
                                  return;
                                }

                                if (_isAtLeastOneCabinetComplete()) {
                                  context.push(RouterPaths.totalPayment,
                                      extra: storeName);
                                } else {
                                  context
                                      .showErrorSnackBar("取り出した商品の写真を撮ってください");
                                }
                              },
                            ),
                          ),
                        ],
                      ),
          ),
        ],
      ),
    );
  }
}

/// Hiển thị dialog xác nhận bypass kết nối khóa
Future<bool?> showBypassConfirmDialog(
    BuildContext context, bool isSeller) async {
  return await showDialog<bool>(
    context: context,
    builder: (context) => Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon
            Assets.aboutOurTeam.image(),

            SizedBox(height: 16.h),

            // Message
            Text(
              isSeller ? '錠と接続せずに進みますが、よろしいですか？' : '錠と接続せずに決済へ進みますが、よろしいですか？',
              style: AppTextStyles.bold(16.sp, color: AppColors.textPrimary),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 16.h),

            // Confirm Button
            CommonButton(
              onPressed: () => Navigator.pop(context, true),
              text: isSeller ? "進む" : '決済を進む',
            ),

            SizedBox(height: 4.h),

            // Cancel Button
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: Text(
                'キャンセル',
                style: AppTextStyles.regular(14.sp,
                    color: AppColors.textLightSecondary),
              ),
            ),
          ],
        ),
      ),
    ),
  );
}
